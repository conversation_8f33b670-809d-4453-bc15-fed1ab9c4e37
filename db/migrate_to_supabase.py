#!/usr/bin/env python3
"""
Supabase Migration Script

Migrates the Form D data storage system from SQLite to Supabase (PostgreSQL):
1. Creates the Supabase schema
2. Migrates existing data (if any)
3. Tests the connection
4. Updates configuration
"""

import os
import sys
import json
import logging
from pathlib import Path
from typing import Dict, List, Any, Optional
from datetime import datetime

# Add project root to path
sys.path.append(str(Path(__file__).parent.parent))

try:
    import psycopg2
    from psycopg2.extras import RealDictCursor
except ImportError:
    print("❌ psycopg2 not installed. Installing...")
    os.system("pip install psycopg2-binary")
    import psycopg2
    from psycopg2.extras import RealDictCursor

try:
    from supabase import create_client, Client
except ImportError:
    print("❌ supabase not installed. Installing...")
    os.system("pip install supabase")
    from supabase import create_client, Client

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class SupabaseMigrator:
    """Handles migration from SQLite to Supabase."""
    
    def __init__(self, supabase_url: str, supabase_key: str, postgres_conn_str: str):
        """
        Initialize the migrator.
        
        Args:
            supabase_url: Supabase project URL
            supabase_key: Supabase service role key
            postgres_conn_str: PostgreSQL connection string
        """
        self.supabase_url = supabase_url
        self.supabase_key = supabase_key
        self.postgres_conn_str = postgres_conn_str
        self.supabase_client = None
        self.postgres_conn = None
        
    def connect(self) -> bool:
        """
        Connect to Supabase and PostgreSQL.
        
        Returns:
            True if successful, False otherwise
        """
        try:
            # Connect to Supabase client
            self.supabase_client = create_client(self.supabase_url, self.supabase_key)
            logger.info("✅ Connected to Supabase client")
            
            # Connect to PostgreSQL directly
            self.postgres_conn = psycopg2.connect(self.postgres_conn_str)
            logger.info("✅ Connected to PostgreSQL database")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Connection failed: {e}")
            return False
    
    def apply_schema(self, schema_path: str = "db/supabase_schema.sql") -> bool:
        """
        Apply the PostgreSQL schema to Supabase.
        
        Args:
            schema_path: Path to the schema file
            
        Returns:
            True if successful, False otherwise
        """
        try:
            schema_file = Path(schema_path)
            if not schema_file.exists():
                logger.error(f"❌ Schema file not found: {schema_path}")
                return False
            
            with open(schema_file, 'r') as f:
                schema_sql = f.read()
            
            cursor = self.postgres_conn.cursor()
            
            # Execute schema in chunks (some extensions might need separate execution)
            statements = schema_sql.split(';')
            
            for statement in statements:
                statement = statement.strip()
                if statement:
                    try:
                        cursor.execute(statement)
                        self.postgres_conn.commit()
                    except Exception as e:
                        logger.warning(f"⚠️ Statement failed (might be expected): {str(e)[:100]}...")
                        self.postgres_conn.rollback()
            
            logger.info("✅ Schema applied successfully")
            return True
            
        except Exception as e:
            logger.error(f"❌ Schema application failed: {e}")
            return False
    
    def test_connection(self) -> bool:
        """
        Test the Supabase connection and basic operations.
        
        Returns:
            True if successful, False otherwise
        """
        try:
            cursor = self.postgres_conn.cursor(cursor_factory=RealDictCursor)
            
            # Test basic query
            cursor.execute("SELECT version();")
            version = cursor.fetchone()
            logger.info(f"✅ PostgreSQL version: {version['version']}")
            
            # Test table creation
            cursor.execute("""
                SELECT table_name 
                FROM information_schema.tables 
                WHERE table_schema = 'public' 
                ORDER BY table_name;
            """)
            tables = cursor.fetchall()
            
            logger.info(f"✅ Found {len(tables)} tables:")
            for table in tables:
                logger.info(f"   📊 {table['table_name']}")
            
            # Test insert/select
            cursor.execute("""
                INSERT INTO zip_files (filename, url, date_str) 
                VALUES ('test.zip', 'http://test.com', '20250101')
                ON CONFLICT (filename) DO NOTHING
                RETURNING id;
            """)
            
            result = cursor.fetchone()
            if result:
                test_id = result['id']
                logger.info(f"✅ Test insert successful, ID: {test_id}")
                
                # Clean up test data
                cursor.execute("DELETE FROM zip_files WHERE filename = 'test.zip';")
                self.postgres_conn.commit()
                logger.info("✅ Test cleanup successful")
            else:
                logger.info("✅ Test insert handled (duplicate)")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Connection test failed: {e}")
            return False
    
    def migrate_sqlite_data(self, sqlite_path: str = "db/formd.db") -> bool:
        """
        Migrate existing data from SQLite to Supabase.
        
        Args:
            sqlite_path: Path to SQLite database
            
        Returns:
            True if successful, False otherwise
        """
        sqlite_file = Path(sqlite_path)
        if not sqlite_file.exists():
            logger.info("ℹ️ No SQLite database found - starting fresh")
            return True
        
        try:
            import sqlite3
            
            sqlite_conn = sqlite3.connect(sqlite_path)
            sqlite_cursor = sqlite_conn.cursor()
            
            # Get table names from SQLite
            sqlite_cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
            tables = [row[0] for row in sqlite_cursor.fetchall()]
            
            logger.info(f"📦 Migrating data from {len(tables)} SQLite tables")
            
            # Migrate each table
            for table_name in tables:
                if table_name == 'sqlite_sequence':
                    continue
                    
                logger.info(f"📊 Migrating table: {table_name}")
                
                # Get data from SQLite
                sqlite_cursor.execute(f"SELECT * FROM {table_name}")
                rows = sqlite_cursor.fetchall()
                
                if not rows:
                    logger.info(f"   ℹ️ No data in {table_name}")
                    continue
                
                # Get column names
                sqlite_cursor.execute(f"PRAGMA table_info({table_name})")
                columns = [col[1] for col in sqlite_cursor.fetchall()]
                
                # Insert into PostgreSQL
                postgres_cursor = self.postgres_conn.cursor()
                
                for row in rows:
                    placeholders = ', '.join(['%s'] * len(row))
                    columns_str = ', '.join(columns)
                    
                    # Handle ID column (SQLite AUTOINCREMENT vs PostgreSQL SERIAL)
                    if 'id' in columns:
                        columns_without_id = [col for col in columns if col != 'id']
                        row_without_id = row[1:]  # Skip first column (id)
                        placeholders = ', '.join(['%s'] * len(row_without_id))
                        columns_str = ', '.join(columns_without_id)
                        
                        insert_sql = f"""
                            INSERT INTO {table_name} ({columns_str}) 
                            VALUES ({placeholders})
                        """
                        postgres_cursor.execute(insert_sql, row_without_id)
                    else:
                        insert_sql = f"""
                            INSERT INTO {table_name} ({columns_str}) 
                            VALUES ({placeholders})
                        """
                        postgres_cursor.execute(insert_sql, row)
                
                self.postgres_conn.commit()
                logger.info(f"   ✅ Migrated {len(rows)} rows")
            
            sqlite_conn.close()
            logger.info("✅ SQLite data migration completed")
            return True
            
        except Exception as e:
            logger.error(f"❌ SQLite migration failed: {e}")
            return False
    
    def close(self):
        """Close connections."""
        if self.postgres_conn:
            self.postgres_conn.close()
            logger.info("🔌 Closed PostgreSQL connection")

def main():
    """Main migration function."""
    print("🚀 Starting Supabase Migration")
    print("=" * 50)
    
    # Check for environment variables
    supabase_url = os.environ.get('SUPABASE_URL')
    supabase_key = os.environ.get('SUPABASE_SERVICE_ROLE_KEY')
    postgres_conn_str = os.environ.get('POSTGRES_CONNECTION_STRING')
    
    if not all([supabase_url, supabase_key, postgres_conn_str]):
        print("❌ Missing environment variables. Please set:")
        print("   - SUPABASE_URL")
        print("   - SUPABASE_SERVICE_ROLE_KEY") 
        print("   - POSTGRES_CONNECTION_STRING")
        print("\nCreate a .env file based on .env.template")
        return False
    
    # Initialize migrator
    migrator = SupabaseMigrator(supabase_url, supabase_key, postgres_conn_str)
    
    try:
        # Step 1: Connect
        if not migrator.connect():
            return False
        
        # Step 2: Apply schema
        if not migrator.apply_schema():
            return False
        
        # Step 3: Test connection
        if not migrator.test_connection():
            return False
        
        # Step 4: Migrate existing data
        if not migrator.migrate_sqlite_data():
            return False
        
        print("\n🎉 Migration completed successfully!")
        print("✅ Supabase is ready for your Form D analysis system")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Migration failed: {e}")
        return False
        
    finally:
        migrator.close()

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
