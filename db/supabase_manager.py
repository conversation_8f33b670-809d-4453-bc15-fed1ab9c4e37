#!/usr/bin/env python3
"""
Supabase Database Manager

Enhanced database manager that uses Supabase (PostgreSQL) instead of SQLite:
1. Real-time capabilities
2. Vector embeddings support
3. Better performance and scalability
4. JSONB support for complex data
"""

import os
import json
import logging
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime, timedelta
from pathlib import Path

try:
    import psycopg2
    from psycopg2.extras import RealDictCursor, Json
except ImportError:
    raise ImportError("psycopg2 not installed. Run: pip install psycopg2-binary")

try:
    from supabase import create_client, Client
except ImportError:
    raise ImportError("supabase not installed. Run: pip install supabase")

try:
    from dotenv import load_dotenv
    load_dotenv()
except ImportError:
    pass

class SupabaseDatabaseManager:
    """
    Enhanced database manager using Supabase for Form D data storage and caching.
    """

    def __init__(self, 
                 supabase_url: str = None, 
                 supabase_key: str = None,
                 postgres_conn_str: str = None):
        """
        Initialize the Supabase database manager.

        Args:
            supabase_url: Supabase project URL (from env if not provided)
            supabase_key: Supabase service role key (from env if not provided)
            postgres_conn_str: PostgreSQL connection string (from env if not provided)
        """
        # Set up logging
        self.logger = logging.getLogger(__name__)
        
        # Get credentials from environment if not provided
        self.supabase_url = supabase_url or os.environ.get('SUPABASE_URL')
        self.supabase_key = supabase_key or os.environ.get('SUPABASE_SERVICE_ROLE_KEY')
        self.postgres_conn_str = postgres_conn_str or os.environ.get('POSTGRES_CONNECTION_STRING')
        
        if not all([self.supabase_url, self.supabase_key, self.postgres_conn_str]):
            raise ValueError("Missing Supabase credentials. Check your .env file.")
        
        # Initialize connections
        self.supabase_client = None
        self.postgres_conn = None
        self._connect()

    def _connect(self) -> None:
        """Initialize connections to Supabase."""
        try:
            # Supabase client for real-time features
            self.supabase_client = create_client(self.supabase_url, self.supabase_key)
            
            # Direct PostgreSQL connection for complex queries
            self.postgres_conn = psycopg2.connect(self.postgres_conn_str)
            
            self.logger.info("✅ Connected to Supabase")
            
        except Exception as e:
            self.logger.error(f"❌ Failed to connect to Supabase: {e}")
            raise

    def get_cursor(self) -> RealDictCursor:
        """Get a database cursor that returns dictionaries."""
        return self.postgres_conn.cursor(cursor_factory=RealDictCursor)

    def commit(self) -> None:
        """Commit the current transaction."""
        self.postgres_conn.commit()

    def rollback(self) -> None:
        """Rollback the current transaction."""
        self.postgres_conn.rollback()

    # ZIP Files Management
    def add_zip_file(self, filename: str, url: str, date_str: str, 
                     file_size: int = None, status: str = 'downloaded') -> int:
        """
        Add a ZIP file record to the database.
        
        Args:
            filename: Name of the ZIP file
            url: URL where the file was downloaded from
            date_str: Date string in YYYYMMDD format
            file_size: Size of the file in bytes
            status: Status of the file (downloaded, extracted, processed, error)
            
        Returns:
            ID of the inserted record
        """
        cursor = self.get_cursor()
        try:
            cursor.execute("""
                INSERT INTO zip_files (filename, url, date_str, file_size, status)
                VALUES (%s, %s, %s, %s, %s)
                ON CONFLICT (filename) DO UPDATE SET
                    last_accessed = NOW(),
                    access_count = zip_files.access_count + 1,
                    status = EXCLUDED.status
                RETURNING id
            """, (filename, url, date_str, file_size, status))
            
            result = cursor.fetchone()
            self.commit()
            
            zip_id = result['id']
            self.logger.info(f"✅ Added ZIP file: {filename} (ID: {zip_id})")
            return zip_id
            
        except Exception as e:
            self.rollback()
            self.logger.error(f"❌ Failed to add ZIP file {filename}: {e}")
            raise

    def get_zip_file(self, filename: str) -> Optional[Dict]:
        """Get ZIP file record by filename."""
        cursor = self.get_cursor()
        try:
            cursor.execute("""
                SELECT * FROM zip_files WHERE filename = %s
            """, (filename,))
            
            result = cursor.fetchone()
            return dict(result) if result else None
            
        except Exception as e:
            self.logger.error(f"❌ Failed to get ZIP file {filename}: {e}")
            return None

    # Form D Filings Management
    def add_form_d_filing(self, filing_data: Dict[str, Any], 
                         source_zip_id: int = None, 
                         source_file: str = None) -> int:
        """
        Add a Form D filing to the database.
        
        Args:
            filing_data: Dictionary containing filing information
            source_zip_id: ID of the source ZIP file
            source_file: Name of the source file
            
        Returns:
            ID of the inserted record
        """
        cursor = self.get_cursor()
        try:
            # Extract key fields from filing data
            accession_number = filing_data.get('accessionNumber', '')
            issuer_name = filing_data.get('issuerName', '')
            filing_date = filing_data.get('filingDate', '')

            # Handle numeric fields that might be "Indefinite" or other non-numeric values
            def safe_numeric(value):
                if value is None or value == '' or str(value).lower() in ['indefinite', 'n/a', 'none']:
                    return None
                try:
                    # Remove commas and convert to float
                    clean_value = str(value).replace(',', '').replace('$', '')
                    return float(clean_value)
                except (ValueError, TypeError):
                    return None

            offering_amount = safe_numeric(filing_data.get('offeringAmount'))
            industry_group = filing_data.get('industryGroup')
            issuer_city = filing_data.get('issuerCity')
            issuer_state = filing_data.get('issuerState')
            issuer_zip = filing_data.get('issuerZip')
            offering_type = filing_data.get('offeringType')
            minimum_investment = safe_numeric(filing_data.get('minimumInvestment'))
            total_amount_sold = safe_numeric(filing_data.get('totalAmountSold'))
            total_remaining = safe_numeric(filing_data.get('totalRemaining'))
            
            cursor.execute("""
                INSERT INTO form_d_filings
                (accession_number, issuer_name, filing_date, offering_amount,
                 industry_group, issuer_city, issuer_state, issuer_zip,
                 offering_type, minimum_investment, total_amount_sold,
                 total_remaining, source_zip_id, source_file, json_data)
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                ON CONFLICT (accession_number) DO UPDATE SET
                    updated_at = NOW(),
                    json_data = EXCLUDED.json_data
                RETURNING id
            """, (accession_number, issuer_name, filing_date, offering_amount,
                  industry_group, issuer_city, issuer_state, issuer_zip,
                  offering_type, minimum_investment, total_amount_sold,
                  total_remaining, source_zip_id, source_file, Json(filing_data)))
            
            result = cursor.fetchone()
            self.commit()
            
            filing_id = result['id']
            self.logger.info(f"✅ Added Form D filing: {accession_number} (ID: {filing_id})")
            return filing_id
            
        except Exception as e:
            self.rollback()
            self.logger.error(f"❌ Failed to add Form D filing: {e}")
            raise

    def get_form_d_filing(self, accession_number: str) -> Optional[Dict]:
        """Get Form D filing by accession number."""
        cursor = self.get_cursor()
        try:
            cursor.execute("""
                SELECT * FROM form_d_filings WHERE accession_number = %s
            """, (accession_number,))
            
            result = cursor.fetchone()
            return dict(result) if result else None
            
        except Exception as e:
            self.logger.error(f"❌ Failed to get Form D filing {accession_number}: {e}")
            return None

    def search_filings(self, 
                      issuer_name: str = None,
                      industry_group: str = None,
                      min_amount: float = None,
                      max_amount: float = None,
                      date_from: str = None,
                      date_to: str = None,
                      limit: int = 100) -> List[Dict]:
        """
        Search Form D filings with various filters.
        
        Args:
            issuer_name: Filter by issuer name (partial match)
            industry_group: Filter by industry group
            min_amount: Minimum offering amount
            max_amount: Maximum offering amount
            date_from: Start date (YYYY-MM-DD)
            date_to: End date (YYYY-MM-DD)
            limit: Maximum number of results
            
        Returns:
            List of matching filings
        """
        cursor = self.get_cursor()
        
        conditions = []
        params = []
        
        if issuer_name:
            conditions.append("issuer_name ILIKE %s")
            params.append(f"%{issuer_name}%")
        
        if industry_group:
            conditions.append("industry_group = %s")
            params.append(industry_group)
        
        if min_amount is not None:
            conditions.append("offering_amount >= %s")
            params.append(min_amount)
        
        if max_amount is not None:
            conditions.append("offering_amount <= %s")
            params.append(max_amount)
        
        if date_from:
            conditions.append("filing_date >= %s")
            params.append(date_from)
        
        if date_to:
            conditions.append("filing_date <= %s")
            params.append(date_to)
        
        where_clause = " AND ".join(conditions) if conditions else "TRUE"
        params.append(limit)
        
        try:
            cursor.execute(f"""
                SELECT * FROM form_d_filings 
                WHERE {where_clause}
                ORDER BY filing_date DESC, created_at DESC
                LIMIT %s
            """, params)
            
            results = cursor.fetchall()
            return [dict(row) for row in results]
            
        except Exception as e:
            self.logger.error(f"❌ Failed to search filings: {e}")
            return []

    def get_stats(self) -> Dict[str, Any]:
        """Get database statistics."""
        cursor = self.get_cursor()
        try:
            stats = {}
            
            # Table counts
            for table in ['zip_files', 'form_d_filings', 'feed_entries', 'analysis_results']:
                cursor.execute(f"SELECT COUNT(*) as count FROM {table}")
                result = cursor.fetchone()
                stats[f"{table}_count"] = result['count']
            
            # Recent activity
            cursor.execute("""
                SELECT COUNT(*) as count FROM form_d_filings 
                WHERE created_at >= NOW() - INTERVAL '24 hours'
            """)
            result = cursor.fetchone()
            stats['filings_last_24h'] = result['count']
            
            # Total offering amounts
            cursor.execute("""
                SELECT 
                    SUM(offering_amount) as total_amount,
                    AVG(offering_amount) as avg_amount,
                    COUNT(*) as count_with_amount
                FROM form_d_filings 
                WHERE offering_amount IS NOT NULL
            """)
            result = cursor.fetchone()
            stats.update(dict(result))
            
            return stats
            
        except Exception as e:
            self.logger.error(f"❌ Failed to get stats: {e}")
            return {}

    def close(self):
        """Close database connections."""
        if self.postgres_conn:
            self.postgres_conn.close()
            self.logger.info("🔌 Closed Supabase connections")

    def __enter__(self):
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        self.close()
