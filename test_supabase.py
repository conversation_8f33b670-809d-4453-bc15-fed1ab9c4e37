#!/usr/bin/env python3
"""
Quick Supabase Connection Test

Tests the Supabase connection before running the full migration.
"""

import os
from pathlib import Path
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def test_env_vars():
    """Test if all required environment variables are set."""
    required_vars = [
        'SUPABASE_URL',
        'SUPABASE_SERVICE_ROLE_KEY',
        'POSTGRES_CONNECTION_STRING'
    ]
    
    missing = []
    for var in required_vars:
        if not os.environ.get(var) or os.environ.get(var).startswith('your_'):
            missing.append(var)
    
    if missing:
        print("❌ Missing or placeholder environment variables:")
        for var in missing:
            print(f"   - {var}")
        print("\nPlease update your .env file with actual Supabase credentials")
        return False
    
    print("✅ All environment variables are set")
    return True

def test_supabase_connection():
    """Test Supabase connection."""
    try:
        from supabase import create_client
        
        url = os.environ.get('SUPABASE_URL')
        key = os.environ.get('SUPABASE_SERVICE_ROLE_KEY')
        
        supabase = create_client(url, key)
        
        # Test a simple query
        result = supabase.table('_realtime_schema_migrations').select('*').limit(1).execute()
        
        print("✅ Supabase client connection successful")
        return True
        
    except Exception as e:
        print(f"❌ Supabase connection failed: {e}")
        return False

def test_postgres_connection():
    """Test direct PostgreSQL connection."""
    try:
        import psycopg2
        
        conn_str = os.environ.get('POSTGRES_CONNECTION_STRING')
        conn = psycopg2.connect(conn_str)
        cursor = conn.cursor()
        
        cursor.execute("SELECT version();")
        version = cursor.fetchone()[0]
        
        conn.close()
        
        print(f"✅ PostgreSQL connection successful")
        print(f"   Version: {version[:50]}...")
        return True
        
    except Exception as e:
        print(f"❌ PostgreSQL connection failed: {e}")
        return False

def main():
    """Main test function."""
    print("🧪 Testing Supabase Connection")
    print("=" * 40)
    
    # Test 1: Environment variables
    if not test_env_vars():
        return False
    
    # Test 2: Supabase client
    if not test_supabase_connection():
        return False
    
    # Test 3: PostgreSQL direct connection
    if not test_postgres_connection():
        return False
    
    print("\n🎉 All tests passed! Ready for migration.")
    print("\nNext steps:")
    print("1. Run: python db/migrate_to_supabase.py")
    print("2. Update your application code to use Supabase")
    
    return True

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
